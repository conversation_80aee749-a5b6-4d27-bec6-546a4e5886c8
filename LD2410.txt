// serial_communication_service.dart

import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:dart_serial_port/dart_serial_port.dart';

class SerialCommunicationService {
  final _buffer = BytesBuilder();
  SerialPort? _port;

  Future<void> initialize() async {
    final availablePorts = SerialPort.availablePorts;
    print('Available ports: $availablePorts');

    for (final portName in availablePorts) {
      final port = SerialPort(portName);
      final config = SerialPortConfig()
        ..baudRate = 115200
        ..stopBits = 1
        ..bits = 8
        ..parity = SerialPortParity.none;

      if (!port.openReadWrite()) {
        print('Failed to open port $portName');
        continue;
      }

      port.config = config;
      _port = port;
      print('Connected to $portName');

      _listenToPort(port);
      break;
    }
  }

  void _listenToPort(SerialPort port) {
    final reader = port.inputStream;
    if (reader == null) {
      print('No input stream');
      return;
    }

    reader.listen((data) {
      _buffer.add(data);
      _processBuffer();
    });
  }

  void _processBuffer() {
    final bytes = _buffer.toBytes();
    if (bytes.length < 12) return; // minimum length check

    final headerIndex = _findFrameHeader(bytes);
    if (headerIndex == -1) {
      _buffer.clear();
      return;
    }

    if (bytes.length < headerIndex + 12) return;

    final length = bytes[headerIndex + 5];
    final frameLength = length + 7; // 7 = header (4) + cmd (1) + len (1) + checksum (1)

    if (bytes.length < headerIndex + frameLength) return;

    final frame = bytes.sublist(headerIndex, headerIndex + frameLength);
    _buffer.clear();
    if (_isValidFrame(frame)) {
      _parseDistanceFrame(frame);
    } else {
      print('Invalid frame received');
    }
  }

  int _findFrameHeader(Uint8List bytes) {
    for (int i = 0; i < bytes.length - 3; i++) {
      if (bytes[i] == 0xF4 && bytes[i + 1] == 0xF3 && bytes[i + 2] == 0xF2 && bytes[i + 3] == 0xF1) {
        return i;
      }
    }
    return -1;
  }

  bool _isValidFrame(List<int> frame) {
    if (frame.length < 8) return false;
    final checksum = frame.last;
    final sum = frame.sublist(4, frame.length - 1).fold(0, (a, b) => a + b);
    return (sum & 0xFF) == checksum;
  }

  void _parseDistanceFrame(List<int> frame) {
    if (frame.length < 18) return;

    final data = Uint8List.fromList(frame);
    final targetState = data[8];
    final movingDist = data[9] | (data[10] << 8);
    final staticDist = data[11] | (data[12] << 8);
    final movingEnergy = data[13];
    final staticEnergy = data[14];

    String target;
    if (targetState == 0x01) {
      target = 'Moving';
    } else if (targetState == 0x02) {
      target = 'Static';
    } else if (targetState == 0x03) {
      target = 'Both';
    } else {
      target = 'None';
    }

    int finalDistance = 0;
    if (movingEnergy > 90) {
      finalDistance = movingDist;
    } else if (staticEnergy > 90) {
      finalDistance = staticDist;
    }

    print(
        'Successfully parsed LD2410 data: Target: $target, Final Distance: $finalDistance cm (Moving: $movingDist/$movingEnergy, Static: $staticDist/$staticEnergy)');
  }
}
