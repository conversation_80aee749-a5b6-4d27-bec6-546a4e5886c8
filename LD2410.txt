import 'dart:async';
import 'dart:typed_data';
import 'package:flutter_libserialport/flutter_libserialport.dart';

class LD2410Data {
  final int movingDistance;
  final int movingEnergy;
  final int staticDistance;
  final int staticEnergy;
  final String targetState;
  final int finalDistance;

  LD2410Data({
    required this.movingDistance,
    required this.movingEnergy,
    required this.staticDistance,
    required this.staticEnergy,
    required this.targetState,
    required this.finalDistance,
  });

  @override
  String toString() {
    return 'Target: $targetState, Final Distance: $finalDistance cm (Moving: $movingDistance/$movingEnergy, Static: $staticDistance/$staticEnergy)';
  }
}

class LD2410SerialService {
  final _controller = StreamController<LD2410Data>.broadcast();
  Stream<LD2410Data> get stream => _controller.stream;

  SerialPort? _port;
  SerialPortReader? _reader;

  Future<void> connect(String portPath) async {
    _port = SerialPort(portPath);

    if (!_port!.openReadWrite()) {
      throw Exception('Failed to open port: ${SerialPort.lastError}');
    }

    final config = _port!.config;
    config.baudRate = 115200;
    config.bits = 8;
    config.stopBits = 1;
    config.parity = SerialPortParity.none;
    _port!.config = config;

    _reader = SerialPortReader(_port!);
    final List<int> buffer = [];

    _reader!.stream.listen((Uint8List chunk) {
      buffer.addAll(chunk);

      while (buffer.length >= 6) {
        final start = buffer.indexWhere((b) => b == 0xAA);
        if (start == -1 || start + 2 >= buffer.length || buffer[start + 1] != 0x02) {
          buffer.removeAt(0);
          continue;
        }

        final length = buffer[start + 2];
        final totalLength = 3 + length;

        if (buffer.length - start < totalLength) break;

        final frame = buffer.sublist(start, start + totalLength);
        final data = parseLD2410DataFrame(frame);
        if (data != null) _controller.add(data);

        buffer.removeRange(0, start + totalLength);
      }
    });
  }

  void dispose() {
    _reader?.close();
    _port?.close();
    _controller.close();
  }

  LD2410Data? parseLD2410DataFrame(List<int> data) {
    if (data.length < 12 || data[0] != 0xAA || data[1] != 0x02) {
      return null;
    }

    try {
      final int targetStateByte = data[4];

      final bool hasMoving = (targetStateByte & 0x01) != 0;
      final bool hasStatic = (targetStateByte & 0x02) != 0;

      final String targetState = switch ((hasMoving, hasStatic)) {
        (true, false) => 'Moving',
        (false, true) => 'Static',
        (true, true) => 'Both',
        _ => 'None',
      };

      final int movingDistance = data[5] + (data[6] << 8);
      final int movingEnergy = data[7];

      final int staticDistance = data[8] + (data[9] << 8);
      final int staticEnergy = data[10];

      int finalDistance = 0;
      if (hasMoving && movingEnergy > 90) {
        finalDistance = movingDistance;
      } else if (hasStatic && staticEnergy > 90) {
        finalDistance = staticDistance;
      }

      return LD2410Data(
        movingDistance: movingDistance,
        movingEnergy: movingEnergy,
        staticDistance: staticDistance,
        staticEnergy: staticEnergy,
        targetState: targetState,
        finalDistance: finalDistance,
      );
    } catch (e) {
      print('Parse error: $e');
      return null;
    }
  }
}

// Example usage
void main() async {
  final service = LD2410SerialService();
  await service.connect('/dev/ttyUSB0'); // Replace with correct port on Windows or Linux

  service.stream.listen((data) {
    print(data);
  });
}
