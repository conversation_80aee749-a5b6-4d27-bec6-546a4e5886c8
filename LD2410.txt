### Dart Function to Parse the Frame

class LD2410Data {
  final int movingDistance;
  final int movingEnergy;
  final int staticDistance;
  final int staticEnergy;
  final String targetState;
  final int finalDistance;

  LD2410Data({
    required this.movingDistance,
    required this.movingEnergy,
    required this.staticDistance,
    required this.staticEnergy,
    required this.targetState,
    required this.finalDistance,
  });

  @override
  String toString() {
    return 'LD2410Data(moving: $movingDistance cm [$movingEnergy], static: $staticDistance cm [$staticEnergy], state: $targetState, final: $finalDistance cm)';
  }
}

LD2410Data? parseLD2410DataFrame(List<int> data) {
  // Sanity check for length
  if (data.length < 12 || data[0] != 0xFD || data[1] != 0xFC) {
    return null;
  }

  final int targetStateByte = data[4];

  final bool hasMoving = (targetStateByte & 0x01) != 0;
  final bool hasStatic = (targetStateByte & 0x02) != 0;

  final String targetState = switch ((hasMoving, hasStatic)) {
    (true, false) => 'Moving',
    (false, true) => 'Static',
    (true, true) => 'Both',
    _ => 'None',
  };

  final int movingDistance = data[5] + (data[6] << 8);
  final int movingEnergy = data[7];

  final int staticDistance = data[8] + (data[9] << 8);
  final int staticEnergy = data[10];

  int finalDistance = 0;

  if (hasMoving && movingEnergy > 90) {
    finalDistance = movingDistance;
  } else if (hasStatic && staticEnergy > 90) {
    finalDistance = staticDistance;
  }

  return LD2410Data(
    movingDistance: movingDistance,
    movingEnergy: movingEnergy,
    staticDistance: staticDistance,
    staticEnergy: staticEnergy,
    targetState: targetState,
    finalDistance: finalDistance,
  );
}

final exampleFrame = [
  0xFD, 0xFC, // Header
  0x0B, 0x00, // Length = 11 bytes
  0x03,       // Target state: moving + static
  0x0A, 0x00, // Moving distance = 10 cm
  0x95,       // Moving energy = 149
  0x14, 0x00, // Static distance = 20 cm
  0x80        // Static energy = 128
];

final result = parseLD2410DataFrame(exampleFrame);
print(result);

