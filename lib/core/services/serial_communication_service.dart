import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:signage/utils/platform_utils.dart';

// Platform-specific imports
import 'package:flutter_libserialport/flutter_libserialport.dart' if (dart.library.html) 'dart:html';
import 'package:usb_serial/usb_serial.dart' if (dart.library.html) 'dart:html';

/// Cross-platform serial communication service for LD2410B sensor
class SerialCommunicationService {
  static final SerialCommunicationService _instance = SerialCommunicationService._internal();
  factory SerialCommunicationService() => _instance;
  SerialCommunicationService._internal();

  // Platform-specific controllers
  SerialPort? _desktopPort;
  UsbPort? _androidPort;
  
  // Connection state
  bool _isConnected = false;
  bool _isInitialized = false;
  
  // Data stream
  StreamController<int?>? _distanceController;
  Stream<int?>? _distanceStream;
  
  // Buffer for incoming data
  List<int> _dataBuffer = [];
  
  // Timer for periodic data reading
  Timer? _readTimer;

  /// Get the distance stream
  Stream<int?>? get distanceStream => _distanceStream;

  /// Check if the service is connected
  bool get isConnected => _isConnected;

  /// Initialize the serial communication service
  Future<bool> initialize() async {
    if (_isInitialized) {
      debugPrint('SerialCommunicationService: Already initialized');
      return _isConnected;
    }

    debugPrint('SerialCommunicationService: Initializing for platform: ${PlatformUtils.platformName}');

    try {
      _distanceController = StreamController<int?>.broadcast();
      _distanceStream = _distanceController!.stream;

      if (PlatformUtils.isAndroid) {
        await _initializeAndroid();
      } else if (PlatformUtils.isDesktop) {
        await _initializeDesktop();
      } else {
        debugPrint('SerialCommunicationService: Unsupported platform');
        _isInitialized = true; // Mark as initialized to prevent retries
        return false;
      }

      _isInitialized = true;
      debugPrint('SerialCommunicationService: Initialization completed. Connected: $_isConnected');
      return _isConnected;
    } catch (e) {
      debugPrint('SerialCommunicationService: Initialization failed: $e');
      _isInitialized = true; // Mark as initialized to prevent retries
      return false;
    }
  }

  /// Initialize Android USB serial communication
  Future<void> _initializeAndroid() async {
    try {
      debugPrint('SerialCommunicationService: Initializing Android USB serial');

      // Get list of available USB devices
      List<UsbDevice> devices = await UsbSerial.listDevices();
      debugPrint('SerialCommunicationService: Found ${devices.length} USB devices');

      // Look for LD2410B device with correct VID/PID
      UsbDevice? targetDevice;
      for (UsbDevice device in devices) {
        debugPrint('SerialCommunicationService: Device - VID: ${device.vid}, PID: ${device.pid}');
        // LD2410B specific VID/PID (CH340 chip)
        // Vendor ID: 6790 (0x1a86), Product ID: 29987 (0x7523)
        if (device.vid == 6790 && device.pid == 29987) {
          debugPrint('SerialCommunicationService: Found LD2410B device (VID: 6790, PID: 29987)');
          targetDevice = device;
          break;
        }
      }

      if (targetDevice == null) {
        debugPrint('SerialCommunicationService: LD2410B device not found');
        return;
      }

      debugPrint('SerialCommunicationService: Found LD2410B device');

      // Create USB port using the correct API
      _androidPort = await targetDevice.create();

      if (_androidPort == null) {
        debugPrint('SerialCommunicationService: Failed to create USB port');
        return;
      }

      // Open the port
      bool openResult = await _androidPort!.open();
      if (!openResult) {
        debugPrint('SerialCommunicationService: Failed to open USB port');
        return;
      }

      // Configure port settings
      await _androidPort!.setDTR(true);
      await _androidPort!.setRTS(true);
      await _androidPort!.setPortParameters(115200, UsbPort.DATABITS_8, UsbPort.STOPBITS_1, UsbPort.PARITY_NONE);

      _isConnected = true;
      debugPrint('SerialCommunicationService: Android USB port opened successfully');

      // Start reading data
      _startAndroidDataReading();

    } catch (e) {
      debugPrint('SerialCommunicationService: Android initialization error: $e');
    }
  }

  /// Initialize desktop serial communication
  Future<void> _initializeDesktop() async {
    try {
      debugPrint('SerialCommunicationService: Initializing desktop serial');

      // Add timeout to prevent hanging
      await Future.any([
        _initializeDesktopWithTimeout(),
        Future.delayed(const Duration(seconds: 10), () {
          debugPrint('SerialCommunicationService: Desktop initialization timeout after 10 seconds');
        }),
      ]);

    } catch (e) {
      debugPrint('SerialCommunicationService: Desktop initialization error: $e');
    }
  }

  /// Initialize desktop serial communication with timeout protection
  Future<void> _initializeDesktopWithTimeout() async {
    try {
      debugPrint('SerialCommunicationService: Starting desktop serial initialization');

      // Add delay before attempting to access ports (as recommended)
      await Future.delayed(const Duration(milliseconds: 500));

      // Get list of available serial ports
      final availablePorts = SerialPort.availablePorts;
      debugPrint('SerialCommunicationService: Available ports: $availablePorts');

      if (availablePorts.isEmpty) {
        debugPrint('SerialCommunicationService: No serial ports available');
        return;
      }

      // Try to find LD2410B device by testing each port
      for (String portName in availablePorts) {
        try {
          debugPrint('SerialCommunicationService: Trying port: $portName');

          // Check if port is available using the recommended method
          if (!SerialPort.availablePorts.contains(portName)) {
            debugPrint('SerialCommunicationService: Port $portName not available');
            continue;
          }

          _desktopPort = SerialPort(portName);

          // Get port info for debugging and check if it's LD2410B
          _logPortInfo(portName);

          // Check if this port matches LD2410B VID/PID
          if (!_isLD2410BDevice(_desktopPort!)) {
            debugPrint('SerialCommunicationService: Port $portName is not LD2410B device, skipping');
            _desktopPort!.dispose();
            _desktopPort = null;
            continue;
          }

          debugPrint('SerialCommunicationService: Port $portName appears to be LD2410B device');

          // Add delay before opening port (as recommended)
          await Future.delayed(const Duration(milliseconds: 100));

          // Try to open the port using the recommended approach
          debugPrint('SerialCommunicationService: Attempting to open port: $portName');

          if (!_desktopPort!.openReadWrite()) {
            final lastError = SerialPort.lastError;
            debugPrint('SerialCommunicationService: Failed to open port: $portName, error: $lastError');
            _desktopPort!.dispose();
            _desktopPort = null;
            continue;
          }

          debugPrint('SerialCommunicationService: Successfully opened port: $portName');

          // Configure port using the recommended approach (get existing config and modify)
          final config = _desktopPort!.config;
          config.baudRate = 115200; // LD2410B working baud rate
          config.bits = 8;
          config.stopBits = 1;
          config.parity = SerialPortParity.none;
          config.setFlowControl(SerialPortFlowControl.none);

          // Apply the configuration
          _desktopPort!.config = config;

          debugPrint('SerialCommunicationService: Port configuration set successfully');
          _isConnected = true;

          // Start reading data
          _startDesktopDataReading();
          break;
        } catch (e) {
          debugPrint('SerialCommunicationService: Error with port $portName: $e');
          _desktopPort?.dispose();
          _desktopPort = null;
        }
      }

      if (!_isConnected) {
        debugPrint('SerialCommunicationService: Could not connect to any serial port');
      }

    } catch (e) {
      debugPrint('SerialCommunicationService: Desktop initialization with timeout error: $e');
    }
  }

  /// Start reading data from Android USB port
  void _startAndroidDataReading() {
    if (_androidPort == null) return;

    debugPrint('SerialCommunicationService: Starting Android data reading');

    // Listen to the input stream from the USB port
    _androidPort!.inputStream!.listen(
      (data) {
        if (data.isNotEmpty) {
          _processIncomingData(data);
        }
      },
      onError: (error) {
        debugPrint('SerialCommunicationService: Android read error: $error');
      },
    );
  }

  /// Start reading data from desktop serial port
  void _startDesktopDataReading() {
    if (_desktopPort == null) return;

    debugPrint('SerialCommunicationService: Starting desktop data reading');

    try {
      // Use SerialPortReader as recommended in the sample code
      final reader = SerialPortReader(_desktopPort!);
      reader.stream.listen(
        (data) {
          // Only log raw data occasionally to reduce spam
          if (data.isNotEmpty && data[0] % 10 == 0) {
            debugPrint('SerialCommunicationService: Received: ${data.map((b) => b.toRadixString(16).padLeft(2, '0')).join(' ')}');
          }
          _processIncomingData(Uint8List.fromList(data));
        },
        onError: (error) {
          debugPrint('SerialCommunicationService: Desktop read error: $error');
        },
        onDone: () {
          debugPrint('SerialCommunicationService: Desktop read stream closed');
        },
      );

      debugPrint('SerialCommunicationService: Desktop data reading started successfully');
    } catch (e) {
      debugPrint('SerialCommunicationService: Desktop read setup error: $e');
    }
  }

  /// Process incoming data and extract distance
  void _processIncomingData(dynamic data) {
    List<int> dataList;
    if (data is List<int>) {
      dataList = data;
    } else {
      debugPrint('SerialCommunicationService: Unknown data type: ${data.runtimeType}');
      return;
    }

    // Add new data to buffer
    _dataBuffer.addAll(dataList);

    // Keep buffer size reasonable
    if (_dataBuffer.length > 1000) {
      _dataBuffer = _dataBuffer.sublist(_dataBuffer.length - 500);
    }

    // Parse distance from buffer
    int? distanceCm = parseDistance(_dataBuffer);
    if (distanceCm != null) {
      // Send the distance in centimeters to the controller
      _distanceController?.add(distanceCm);
    }
  }

  /// Parse distance from LD2410B data
  /// Returns distance in centimeters or null if no valid data found
  int? parseDistance(List<int> data) {
    // LD2410B protocol: F4 F3 F2 F1 [length] [command] [data...] F8 F7 F6 F5
    for (int i = 0; i < data.length - 12; i++) {
      // Look for LD2410B frame header: F4 F3 F2 F1
      if (data[i] == 0xF4 && data[i + 1] == 0xF3 && data[i + 2] == 0xF2 && data[i + 3] == 0xF1) {
        // Check if we have enough data for a complete frame
        if (i + 12 < data.length) {
          // Check for target detection data (command 0x02)
          if (data[i + 6] == 0x02 && data[i + 7] == 0xAA) {
            // Distance data is typically at offset 8-9 from command
            if (i + 15 < data.length) {
              int distanceLow = data[i + 14];
              int distanceHigh = data[i + 15];
              int distanceCm = (distanceHigh << 8) + distanceLow;

              // Only log occasionally to reduce spam
              //if (distanceCm % 100 == 0) {
                debugPrint("*************************************************");
                debugPrint('SerialCommunicationService: Parsed distance: ${distanceCm}cm (bytes: ${distanceLow.toRadixString(16)} ${distanceHigh.toRadixString(16)})');
                debugPrint("*************************************************");
              //}

              if (distanceCm > 0 && distanceCm < 1000) { // Reasonable range check
                return distanceCm;
              }
            }
          }
        }
      }
    }
    return null;
  }

  /// Dispose the service and clean up resources
  Future<void> dispose() async {
    debugPrint('SerialCommunicationService: Disposing');
    
    _readTimer?.cancel();
    _readTimer = null;
    
    if (PlatformUtils.isAndroid && _androidPort != null) {
      await _androidPort!.close();
      _androidPort = null;
    }
    
    if (PlatformUtils.isDesktop && _desktopPort != null) {
      _desktopPort!.close();
      _desktopPort!.dispose();
      _desktopPort = null;
    }
    
    await _distanceController?.close();
    _distanceController = null;
    _distanceStream = null;
    
    _isConnected = false;
    _isInitialized = false;
    _dataBuffer.clear();
  }



  /// Log port information for debugging
  void _logPortInfo(String portName) {
    try {
      debugPrint('SerialCommunicationService: Port info for $portName:');
      debugPrint('  - Vendor ID: ${_desktopPort?.vendorId ?? 'N/A'}');
      debugPrint('  - Product ID: ${_desktopPort?.productId ?? 'N/A'}');
      debugPrint('  - Description: ${_desktopPort?.description ?? 'N/A'}');
    } catch (e) {
      debugPrint('SerialCommunicationService: Error getting port info for $portName: $e');
    }
  }



  /// Check if the serial port is an LD2410B device
  bool _isLD2410BDevice(SerialPort port) {
    try {
      // Check vendor ID and product ID
      final vendorId = port.vendorId;
      final productId = port.productId;

      debugPrint('SerialCommunicationService: Port VID: $vendorId, PID: $productId');

      // LD2410B uses CH340 chip: VID=6790 (0x1a86), PID=29987 (0x7523)
      if (vendorId == 6790 && productId == 29987) {
        debugPrint('SerialCommunicationService: Confirmed LD2410B device (VID: 6790, PID: 29987)');
        return true;
      }

      // Also check hex values in case the library returns hex
      if (vendorId == 0x1a86 && productId == 0x7523) {
        debugPrint('SerialCommunicationService: Confirmed LD2410B device (VID: 0x1a86, PID: 0x7523)');
        return true;
      }

      debugPrint('SerialCommunicationService: Not LD2410B device (VID: $vendorId, PID: $productId)');
      return false;
    } catch (e) {
      debugPrint('SerialCommunicationService: Error checking device VID/PID: $e');
      // If we can't determine the device type, assume it might be LD2410B
      return true;
    }
  }
}
