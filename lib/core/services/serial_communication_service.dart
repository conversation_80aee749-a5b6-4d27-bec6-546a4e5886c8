import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:signage/utils/platform_utils.dart';

// Platform-specific imports
import 'package:flutter_libserialport/flutter_libserialport.dart' if (dart.library.html) 'dart:html';
import 'package:usb_serial/usb_serial.dart' if (dart.library.html) 'dart:html';

/// Data structure for LD2410B sensor readings
class LD2410Data {
  final int movingDistance;
  final int movingEnergy;
  final int staticDistance;
  final int staticEnergy;
  final String targetState;
  final int finalDistance;

  LD2410Data({
    required this.movingDistance,
    required this.movingEnergy,
    required this.staticDistance,
    required this.staticEnergy,
    required this.targetState,
    required this.finalDistance,
  });

  @override
  String toString() {
    return 'Target: $targetState, Final Distance: $finalDistance cm (Moving: $movingDistance/$movingEnergy, Static: $staticDistance/$staticEnergy)';
  }
}

/// Cross-platform serial communication service for LD2410B sensor
class SerialCommunicationService {
  static final SerialCommunicationService _instance = SerialCommunicationService._internal();
  factory SerialCommunicationService() => _instance;
  SerialCommunicationService._internal();

  // Platform-specific controllers
  SerialPort? _desktopPort;
  UsbPort? _androidPort;
  
  // Connection state
  bool _isConnected = false;
  bool _isInitialized = false;
  
  // Data stream
  StreamController<int?>? _distanceController;
  Stream<int?>? _distanceStream;
  
  // Buffer for incoming data
  List<int> _dataBuffer = [];
  
  // Timer for periodic data reading
  Timer? _readTimer;

  /// Get the distance stream
  Stream<int?>? get distanceStream => _distanceStream;

  /// Check if the service is connected
  bool get isConnected => _isConnected;

  /// Initialize the serial communication service
  Future<bool> initialize() async {
    if (_isInitialized) {
      debugPrint('SerialCommunicationService: Already initialized');
      return _isConnected;
    }

    debugPrint('SerialCommunicationService: Initializing for platform: ${PlatformUtils.platformName}');

    try {
      _distanceController = StreamController<int?>.broadcast();
      _distanceStream = _distanceController!.stream;

      if (PlatformUtils.isAndroid) {
        await _initializeAndroid();
      } else if (PlatformUtils.isDesktop) {
        await _initializeDesktop();
      } else {
        debugPrint('SerialCommunicationService: Unsupported platform');
        _isInitialized = true; // Mark as initialized to prevent retries
        return false;
      }

      _isInitialized = true;
      debugPrint('SerialCommunicationService: Initialization completed. Connected: $_isConnected');
      return _isConnected;
    } catch (e) {
      debugPrint('SerialCommunicationService: Initialization failed: $e');
      _isInitialized = true; // Mark as initialized to prevent retries
      return false;
    }
  }

  /// Initialize Android USB serial communication
  Future<void> _initializeAndroid() async {
    try {
      debugPrint('SerialCommunicationService: Initializing Android USB serial');

      // Get list of available USB devices
      List<UsbDevice> devices = await UsbSerial.listDevices();
      debugPrint('SerialCommunicationService: Found ${devices.length} USB devices');

      // Look for LD2410B device with correct VID/PID
      UsbDevice? targetDevice;
      for (UsbDevice device in devices) {
        debugPrint('SerialCommunicationService: Device - VID: ${device.vid}, PID: ${device.pid}');
        // LD2410B specific VID/PID (CH340 chip)
        // Vendor ID: 6790 (0x1a86), Product ID: 29987 (0x7523)
        if (device.vid == 6790 && device.pid == 29987) {
          debugPrint('SerialCommunicationService: Found LD2410B device (VID: 6790, PID: 29987)');
          targetDevice = device;
          break;
        }
      }

      if (targetDevice == null) {
        debugPrint('SerialCommunicationService: LD2410B device not found');
        return;
      }

      debugPrint('SerialCommunicationService: Found LD2410B device');

      // Create USB port using the correct API
      _androidPort = await targetDevice.create();

      if (_androidPort == null) {
        debugPrint('SerialCommunicationService: Failed to create USB port');
        return;
      }

      // Open the port
      bool openResult = await _androidPort!.open();
      if (!openResult) {
        debugPrint('SerialCommunicationService: Failed to open USB port');
        return;
      }

      // Configure port settings
      await _androidPort!.setDTR(true);
      await _androidPort!.setRTS(true);
      await _androidPort!.setPortParameters(115200, UsbPort.DATABITS_8, UsbPort.STOPBITS_1, UsbPort.PARITY_NONE);

      _isConnected = true;
      debugPrint('SerialCommunicationService: Android USB port opened successfully');

      // Start reading data
      _startAndroidDataReading();

    } catch (e) {
      debugPrint('SerialCommunicationService: Android initialization error: $e');
    }
  }

  /// Initialize desktop serial communication
  Future<void> _initializeDesktop() async {
    try {
      debugPrint('SerialCommunicationService: Initializing desktop serial');

      // Add timeout to prevent hanging
      await Future.any([
        _initializeDesktopWithTimeout(),
        Future.delayed(const Duration(seconds: 10), () {
          debugPrint('SerialCommunicationService: Desktop initialization timeout after 10 seconds');
        }),
      ]);

    } catch (e) {
      debugPrint('SerialCommunicationService: Desktop initialization error: $e');
    }
  }

  /// Initialize desktop serial communication with timeout protection
  Future<void> _initializeDesktopWithTimeout() async {
    try {
      debugPrint('SerialCommunicationService: Starting desktop serial initialization');

      // Add delay before attempting to access ports (as recommended)
      await Future.delayed(const Duration(milliseconds: 500));

      // Get list of available serial ports
      final availablePorts = SerialPort.availablePorts;
      debugPrint('SerialCommunicationService: Available ports: $availablePorts');

      if (availablePorts.isEmpty) {
        debugPrint('SerialCommunicationService: No serial ports available');
        return;
      }

      // Try to find LD2410B device by testing each port
      for (String portName in availablePorts) {
        try {
          debugPrint('SerialCommunicationService: Trying port: $portName');

          // Check if port is available using the recommended method
          if (!SerialPort.availablePorts.contains(portName)) {
            debugPrint('SerialCommunicationService: Port $portName not available');
            continue;
          }

          _desktopPort = SerialPort(portName);

          // Get port info for debugging and check if it's LD2410B
          _logPortInfo(portName);

          // Check if this port matches LD2410B VID/PID
          if (!_isLD2410BDevice(_desktopPort!)) {
            debugPrint('SerialCommunicationService: Port $portName is not LD2410B device, skipping');
            _desktopPort!.dispose();
            _desktopPort = null;
            continue;
          }

          debugPrint('SerialCommunicationService: Port $portName appears to be LD2410B device');

          // Add delay before opening port (as recommended)
          await Future.delayed(const Duration(milliseconds: 100));

          // Try to open the port using the recommended approach
          debugPrint('SerialCommunicationService: Attempting to open port: $portName');

          if (!_desktopPort!.openReadWrite()) {
            final lastError = SerialPort.lastError;
            debugPrint('SerialCommunicationService: Failed to open port: $portName, error: $lastError');
            _desktopPort!.dispose();
            _desktopPort = null;
            continue;
          }

          debugPrint('SerialCommunicationService: Successfully opened port: $portName');

          // Configure port using the recommended approach (get existing config and modify)
          final config = _desktopPort!.config;
          config.baudRate = 115200; // LD2410B working baud rate
          config.bits = 8;
          config.stopBits = 1;
          config.parity = SerialPortParity.none;
          config.setFlowControl(SerialPortFlowControl.none);

          // Apply the configuration
          _desktopPort!.config = config;

          debugPrint('SerialCommunicationService: Port configuration set successfully');
          _isConnected = true;

          // Start reading data
          _startDesktopDataReading();
          break;
        } catch (e) {
          debugPrint('SerialCommunicationService: Error with port $portName: $e');
          _desktopPort?.dispose();
          _desktopPort = null;
        }
      }

      if (!_isConnected) {
        debugPrint('SerialCommunicationService: Could not connect to any serial port');
      }

    } catch (e) {
      debugPrint('SerialCommunicationService: Desktop initialization with timeout error: $e');
    }
  }

  /// Start reading data from Android USB port
  void _startAndroidDataReading() {
    if (_androidPort == null) return;

    debugPrint('SerialCommunicationService: Starting Android data reading');

    // Listen to the input stream from the USB port
    _androidPort!.inputStream!.listen(
      (data) {
        if (data.isNotEmpty) {
          _processIncomingData(data);
        }
      },
      onError: (error) {
        debugPrint('SerialCommunicationService: Android read error: $error');
      },
    );
  }

  /// Start reading data from desktop serial port
  void _startDesktopDataReading() {
    if (_desktopPort == null) return;

    debugPrint('SerialCommunicationService: Starting desktop data reading');

    try {
      // Use SerialPortReader as recommended in the sample code
      final reader = SerialPortReader(_desktopPort!);
      reader.stream.listen(
        (data) {
          // Always log that we received data for debugging
          if (data.isNotEmpty) {
            debugPrint('SerialCommunicationService: Received ${data.length} bytes: ${data.map((b) => b.toRadixString(16).padLeft(2, '0')).join(' ')}');
          }
          _processIncomingData(Uint8List.fromList(data));
        },
        onError: (error) {
          debugPrint('SerialCommunicationService: Desktop read error: $error');
        },
        onDone: () {
          debugPrint('SerialCommunicationService: Desktop read stream closed');
        },
      );

      debugPrint('SerialCommunicationService: Desktop data reading started successfully');
    } catch (e) {
      debugPrint('SerialCommunicationService: Desktop read setup error: $e');
    }
  }

  /// Process incoming data and extract distance
  void _processIncomingData(dynamic data) {
    List<int> dataList;
    if (data is List<int>) {
      dataList = data;
    } else {
      debugPrint('SerialCommunicationService: Unknown data type: ${data.runtimeType}');
      return;
    }

    debugPrint('SerialCommunicationService: Processing ${dataList.length} bytes, buffer size before: ${_dataBuffer.length}');

    // Add new data to buffer
    _dataBuffer.addAll(dataList);

    debugPrint('SerialCommunicationService: Buffer size after adding data: ${_dataBuffer.length}');

    // Keep buffer size reasonable
    if (_dataBuffer.length > 1000) {
      _dataBuffer = _dataBuffer.sublist(_dataBuffer.length - 500);
      //debugPrint('SerialCommunicationService: Buffer trimmed to ${_dataBuffer.length} bytes');
    }

    // Process frames from buffer (similar to LD2410.txt approach)
    _processFramesFromBuffer();
  }

  /// Process complete frames from the buffer
  void _processFramesFromBuffer() {
    while (_dataBuffer.length >= 6) {
      // Find frame start (0xAA 0x02)
      final start = _dataBuffer.indexWhere((b) => b == 0xAA);
      if (start == -1 || start + 2 >= _dataBuffer.length || _dataBuffer[start + 1] != 0x02) {
        if (start == -1) {
          // No frame header found, clear buffer
          //debugPrint('SerialCommunicationService: No frame header found, clearing buffer');
          _dataBuffer.clear();
          break;
        } else {
          // Remove invalid byte and continue searching
          _dataBuffer.removeAt(0);
          continue;
        }
      }

      final length = _dataBuffer[start + 2];
      final totalLength = 3 + length; // header (2) + length (1) + data

      debugPrint('SerialCommunicationService: Found frame at position $start, length: $length, total: $totalLength');

      // Check if we have complete frame
      if (_dataBuffer.length - start < totalLength) {
        debugPrint('SerialCommunicationService: Incomplete frame, waiting for more data');
        break;
      }

      // Extract frame
      final frame = _dataBuffer.sublist(start, start + totalLength);

      // Parse frame
      final ld2410Data = parseLD2410DataFrame(frame);
      if (ld2410Data != null) {
        // Send the finalDistance in centimeters to the controller
        _distanceController?.add(ld2410Data.finalDistance);
        debugPrint('SerialCommunicationService: Successfully parsed LD2410 data: $ld2410Data');
      } else {
        debugPrint('SerialCommunicationService: Failed to parse frame');
      }

      // Remove processed frame from buffer
      _dataBuffer.removeRange(0, start + totalLength);
      //debugPrint('SerialCommunicationService: Removed processed frame, buffer size now: ${_dataBuffer.length}');
    }

    // Show buffer preview if no frames could be processed
    if (_dataBuffer.isNotEmpty && _dataBuffer.length < 6) {
      final previewBytes = _dataBuffer.take(20).map((b) => b.toRadixString(16).padLeft(2, '0')).join(' ');
      debugPrint('SerialCommunicationService: Buffer preview (waiting for more data): $previewBytes');
    }
  }

  /// Parse LD2410B data frame with energy values and final distance logic
  /// Returns LD2410Data object or null if no valid data found
  /// Expects a complete frame starting with 0xAA 0x02
  LD2410Data? parseLD2410DataFrame(List<int> data) {
    // Validate frame header and minimum length
    if (data.length < 12 || data[0] != 0xAA || data[1] != 0x02) {
      debugPrint('SerialCommunicationService: parseLD2410DataFrame - Invalid frame header or too short (${data.length} bytes)');
      return null;
    }

    try {
      final int targetStateByte = data[4];

      final bool hasMoving = (targetStateByte & 0x01) != 0;
      final bool hasStatic = (targetStateByte & 0x02) != 0;

      final String targetState = switch ((hasMoving, hasStatic)) {
        (true, false) => 'Moving',
        (false, true) => 'Static',
        (true, true) => 'Both',
        _ => 'None',
      };

      final int movingDistance = data[5] + (data[6] << 8);
      final int movingEnergy = data[7];

      final int staticDistance = data[8] + (data[9] << 8);
      final int staticEnergy = data[10];

      int finalDistance = 0;

      if (hasMoving && movingEnergy > 90) {
        finalDistance = movingDistance;
      } else if (hasStatic && staticEnergy > 90) {
        finalDistance = staticDistance;
      }

      //debugPrint('SerialCommunicationService: Successfully parsed frame - targetState: $targetState, finalDistance: $finalDistance');

      return LD2410Data(
        movingDistance: movingDistance,
        movingEnergy: movingEnergy,
        staticDistance: staticDistance,
        staticEnergy: staticEnergy,
        targetState: targetState,
        finalDistance: finalDistance,
      );
    } catch (e) {
      debugPrint('SerialCommunicationService: Parse error: $e');
      return null;
    }
  }

  /// Dispose the service and clean up resources
  Future<void> dispose() async {
    debugPrint('SerialCommunicationService: Disposing');
    
    _readTimer?.cancel();
    _readTimer = null;
    
    if (PlatformUtils.isAndroid && _androidPort != null) {
      await _androidPort!.close();
      _androidPort = null;
    }
    
    if (PlatformUtils.isDesktop && _desktopPort != null) {
      _desktopPort!.close();
      _desktopPort!.dispose();
      _desktopPort = null;
    }
    
    await _distanceController?.close();
    _distanceController = null;
    _distanceStream = null;
    
    _isConnected = false;
    _isInitialized = false;
    _dataBuffer.clear();
  }



  /// Log port information for debugging
  void _logPortInfo(String portName) {
    try {
      debugPrint('SerialCommunicationService: Port info for $portName:');
      debugPrint('  - Vendor ID: ${_desktopPort?.vendorId ?? 'N/A'}');
      debugPrint('  - Product ID: ${_desktopPort?.productId ?? 'N/A'}');
      debugPrint('  - Description: ${_desktopPort?.description ?? 'N/A'}');
    } catch (e) {
      debugPrint('SerialCommunicationService: Error getting port info for $portName: $e');
    }
  }



  /// Check if the serial port is an LD2410B device
  bool _isLD2410BDevice(SerialPort port) {
    try {
      // Check vendor ID and product ID
      final vendorId = port.vendorId;
      final productId = port.productId;

      debugPrint('SerialCommunicationService: Port VID: $vendorId, PID: $productId');

      // LD2410B uses CH340 chip: VID=6790 (0x1a86), PID=29987 (0x7523)
      if (vendorId == 6790 && productId == 29987) {
        debugPrint('SerialCommunicationService: Confirmed LD2410B device (VID: 6790, PID: 29987)');
        return true;
      }

      // Also check hex values in case the library returns hex
      if (vendorId == 0x1a86 && productId == 0x7523) {
        debugPrint('SerialCommunicationService: Confirmed LD2410B device (VID: 0x1a86, PID: 0x7523)');
        return true;
      }

      debugPrint('SerialCommunicationService: Not LD2410B device (VID: $vendorId, PID: $productId)');
      return false;
    } catch (e) {
      debugPrint('SerialCommunicationService: Error checking device VID/PID: $e');
      // If we can't determine the device type, assume it might be LD2410B
      return true;
    }
  }
}
